# Generated by Django 5.2.1 on 2025-05-27 18:27

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("patients", "0001_initial"),
        ("users", "0015_doctors_consult_fee_doctors_consult_hour"),
    ]

    operations = [
        migrations.CreateModel(
            name="Status",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("status", models.CharField(max_length=20)),
            ],
            options={
                "verbose_name": "Status",
                "verbose_name_plural": "Status",
            },
        ),
        migrations.CreateModel(
            name="Time",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("time", models.CharField(max_length=20)),
                ("duration_minutes", models.PositiveIntegerField()),
            ],
            options={
                "verbose_name": "Time",
                "verbose_name_plural": "Times",
            },
        ),
        migrations.AlterModelOptions(
            name="appointment",
            options={
                "verbose_name": "Appointment",
                "verbose_name_plural": "Appointments",
            },
        ),
        migrations.RemoveField(
            model_name="appointment",
            name="patient_id",
        ),
        migrations.RemoveField(
            model_name="appointment",
            name="start_time",
        ),
        migrations.RemoveField(
            model_name="appointment",
            name="status",
        ),
        migrations.RemoveField(
            model_name="appointment",
            name="summary",
        ),
        migrations.AddField(
            model_name="appointment",
            name="amount",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="appointment",
            name="payment_status",
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name="appointment",
            name="doctor",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="users.doctors"
            ),
        ),
        migrations.AlterField(
            model_name="appointment",
            name="patient",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="users.patients"
            ),
        ),
        migrations.AlterField(
            model_name="appointment",
            name="start_date",
            field=models.DateTimeField(),
        ),
        migrations.AddField(
            model_name="appointment",
            name="time",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                to="patients.time",
            ),
        ),
    ]
