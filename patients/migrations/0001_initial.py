# Generated by Django 5.0.1 on 2024-01-09 18:27

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Appointment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('doctor', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('patient', models.Char<PERSON>ield(max_length=255)),
                ('patient_id', models.IntegerField()),
                ('status', models.CharField(max_length=255)),
                ('summary', models.TextField()),
                ('description', models.TextField()),
                ('start_date', models.DateField()),
                ('start_time', models.TimeField()),
            ],
        ),
    ]
