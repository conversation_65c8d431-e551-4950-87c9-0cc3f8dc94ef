# Generated by Django 4.2.9 on 2024-01-06 15:28

import django.contrib.auth.models
from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Users',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.<PERSON>olean<PERSON>ield(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('user_status', models.CharField(blank=True, max_length=500)),
                ('first_name', models.TextField(blank=True, max_length=500)),
                ('last_name', models.TextField(blank=True, max_length=500)),
                ('profile_pic', models.ImageField(blank=True, default='doctor/profiles/download.png', upload_to='doctor/profiles')),
                ('username', models.CharField(max_length=30, unique=True)),
                ('email', models.CharField(max_length=30)),
                ('sex', models.TextField(choices=[('Male', 'Male'), ('Female', 'Female')], default='not_known', max_length=10)),
                ('password', models.TextField(max_length=30)),
                ('confirm_password', models.TextField(max_length=30)),
                ('address', models.TextField(blank=True, max_length=30)),
                ('groups', models.ManyToManyField(related_name='custom_user_groups', to='auth.group')),
                ('user_permissions', models.ManyToManyField(related_name='custom_user_permissions', to='auth.permission')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
