# Generated by Django 4.2.9 on 2024-01-06 15:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id_address', models.AutoField(primary_key=True, serialize=False)),
                ('address_line', models.CharField(max_length=50)),
                ('city', models.CharField(max_length=50)),
                ('code_postal', models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name='Doctors',
            fields=[
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to='users.users')),
                ('specialty', models.CharField(max_length=50)),
                ('bio', models.TextField()),
            ],
        ),
        migrations.CreateModel(
            name='Patients',
            fields=[
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to='users.users')),
                ('insurance', models.CharField(max_length=50)),
            ],
        ),
        migrations.RemoveField(
            model_name='users',
            name='address',
        ),
        migrations.RemoveField(
            model_name='users',
            name='confirm_password',
        ),
        migrations.RemoveField(
            model_name='users',
            name='profile_pic',
        ),
        migrations.RemoveField(
            model_name='users',
            name='sex',
        ),
        migrations.RemoveField(
            model_name='users',
            name='user_status',
        ),
        migrations.AddField(
            model_name='users',
            name='birthday',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='users',
            name='gender',
            field=models.CharField(choices=[('Male', 'Male'), ('Female', 'Female')], default='not_known', max_length=10),
        ),
        migrations.AddField(
            model_name='users',
            name='profile_avatar',
            field=models.ImageField(blank=True, default='doctor/profiles/download.png', upload_to='users/profiles'),
        ),
        migrations.AlterField(
            model_name='users',
            name='email',
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name='users',
            name='first_name',
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name='users',
            name='last_name',
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name='users',
            name='password',
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name='users',
            name='username',
            field=models.CharField(max_length=50, unique=True),
        ),
        migrations.AddField(
            model_name='users',
            name='id_address',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='users.address'),
        ),
    ]
