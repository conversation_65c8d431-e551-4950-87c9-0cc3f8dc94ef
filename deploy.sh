#!/bin/bash

# Go<PERSON><PERSON><PERSON>'s Care Deployment Script

echo "🏥 Deploying <PERSON>Doctor's Care..."

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Collect static files
echo "📁 Collecting static files..."
python manage.py collectstatic --noinput

# Run migrations
echo "🗄️ Running database migrations..."
python manage.py migrate

# Create superuser (optional - comment out if not needed)
# echo "👤 Creating superuser..."
# python manage.py createsuperuser --noinput

echo "✅ Deployment completed successfully!"
echo "🚀 Starting GoDoctor's Care with gunicorn..."

# Start the application
gunicorn hospital.wsgi:application --bind 0.0.0.0:8000
