{% extends 'base.html' %}
{% load static %}

{% block title %}Book Appointment{% endblock title %}

{% block content %}
<!-- Page Header -->
<section class="bg-primary text-white py-5" style="margin-top: 76px;">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto text-center">
        <h1 class="display-4 fw-bold">Book an Appointment</h1>
        <p class="lead">Schedule your visit with our expert medical professionals</p>
      </div>
    </div>
  </div>
</section>

<!-- Appointment Form -->
<section class="py-5">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        {% if not user.is_authenticated %}
          <div class="alert alert-info" role="alert">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Please note:</strong> You need to <a href="{% url 'login' %}" class="alert-link">login</a> or 
            <a href="{% url 'register' %}" class="alert-link">register</a> to book an appointment.
          </div>
        {% endif %}
        
        <div class="card border-0 shadow">
          <div class="card-body p-5">
            <h3 class="text-center mb-4">Appointment Details</h3>

            {% if messages %}
              {% for message in messages %}
                {% if 'success' in message.tags %}
                  <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                  </div>
                {% else %}
                  <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                  </div>
                {% endif %}
              {% endfor %}
            {% endif %}
            
            <form method="POST" class="needs-validation" novalidate>
              {% csrf_token %}
              
              <div class="row g-3">
                <!-- Patient Information -->
                <div class="col-12">
                  <h5 class="text-primary mb-3">Patient Information</h5>
                </div>
                
                <div class="col-md-6">
                  <label for="firstName" class="form-label">First Name</label>
                  <input type="text" class="form-control" id="firstName" name="first_name" 
                         value="{% if user.is_authenticated %}{{ user.first_name }}{% endif %}" required>
                  <div class="invalid-feedback">Please provide your first name.</div>
                </div>
                
                <div class="col-md-6">
                  <label for="lastName" class="form-label">Last Name</label>
                  <input type="text" class="form-control" id="lastName" name="last_name" 
                         value="{% if user.is_authenticated %}{{ user.last_name }}{% endif %}" required>
                  <div class="invalid-feedback">Please provide your last name.</div>
                </div>
                
                <div class="col-md-6">
                  <label for="email" class="form-label">Email</label>
                  <input type="email" class="form-control" id="email" name="email" 
                         value="{% if user.is_authenticated %}{{ user.email }}{% endif %}" required>
                  <div class="invalid-feedback">Please provide a valid email.</div>
                </div>
                
                <div class="col-md-6">
                  <label for="phone" class="form-label">Phone Number</label>
                  <input type="tel" class="form-control" id="phone" name="phone" required>
                  <div class="invalid-feedback">Please provide your phone number.</div>
                </div>
                
                <!-- Appointment Details -->
                <div class="col-12 mt-4">
                  <h5 class="text-primary mb-3">Appointment Details</h5>
                </div>
                
                <div class="col-md-6">
                  <label for="specialty" class="form-label">Medical Specialty</label>
                  <select class="form-select" id="specialty" name="specialty" required>
                    <option value="">Choose specialty...</option>
                    {% for specialty in specialties %}
                      <option value="{{ specialty.id }}">{{ specialty.name }}</option>
                    {% endfor %}
                  </select>
                  <div class="invalid-feedback">Please select a specialty.</div>
                </div>
                
                <div class="col-md-6">
                  <label for="doctor" class="form-label">Preferred Doctor</label>
                  <select class="form-select" id="doctor" name="doctor">
                    <option value="">Any available doctor</option>
                    {% for doctor in doctors %}
                      <option value="{{ doctor.id }}" data-specialty="{{ doctor.specialty.id }}">
                        Dr. {{ doctor.user.first_name }} {{ doctor.user.last_name }} - {{ doctor.specialty.name }}
                      </option>
                    {% endfor %}
                  </select>
                </div>
                
                <div class="col-md-6">
                  <label for="appointmentDate" class="form-label">Preferred Date</label>
                  <input type="date" class="form-control" id="appointmentDate" name="appointment_date" required>
                  <div class="invalid-feedback">Please select a date.</div>
                </div>
                
                <div class="col-md-6">
                  <label for="appointmentTime" class="form-label">Preferred Time</label>
                  <select class="form-select" id="appointmentTime" name="appointment_time" required>
                    <option value="">Choose time...</option>
                    <option value="09:00">9:00 AM</option>
                    <option value="09:30">9:30 AM</option>
                    <option value="10:00">10:00 AM</option>
                    <option value="10:30">10:30 AM</option>
                    <option value="11:00">11:00 AM</option>
                    <option value="11:30">11:30 AM</option>
                    <option value="14:00">2:00 PM</option>
                    <option value="14:30">2:30 PM</option>
                    <option value="15:00">3:00 PM</option>
                    <option value="15:30">3:30 PM</option>
                    <option value="16:00">4:00 PM</option>
                    <option value="16:30">4:30 PM</option>
                  </select>
                  <div class="invalid-feedback">Please select a time.</div>
                </div>
                
                <div class="col-12">
                  <label for="reason" class="form-label">Reason for Visit</label>
                  <textarea class="form-control" id="reason" name="reason" rows="4" 
                            placeholder="Please describe your symptoms or reason for the appointment..." required></textarea>
                  <div class="invalid-feedback">Please provide a reason for your visit.</div>
                </div>
                
                <div class="col-12">
                  <label for="notes" class="form-label">Additional Notes (Optional)</label>
                  <textarea class="form-control" id="notes" name="notes" rows="3" 
                            placeholder="Any additional information you'd like to share..."></textarea>
                </div>
                
                <!-- Terms and Conditions -->
                <div class="col-12 mt-4">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                    <label class="form-check-label" for="terms">
                      I agree to the <a href="#" class="text-primary">Terms and Conditions</a> and 
                      <a href="#" class="text-primary">Privacy Policy</a>
                    </label>
                    <div class="invalid-feedback">You must agree to the terms and conditions.</div>
                  </div>
                </div>
                
                <div class="col-12 text-center mt-4">
                  {% if user.is_authenticated %}
                    <button type="submit" class="btn btn-primary btn-lg px-5">
                      <i class="fas fa-calendar-check me-2"></i>Book Appointment
                    </button>
                  {% else %}
                    <div class="d-flex gap-3 justify-content-center">
                      <a href="{% url 'login' %}" class="btn btn-primary btn-lg px-4">
                        <i class="fas fa-sign-in-alt me-2"></i>Login to Book
                      </a>
                      <a href="{% url 'register' %}" class="btn btn-outline-primary btn-lg px-4">
                        <i class="fas fa-user-plus me-2"></i>Register
                      </a>
                    </div>
                  {% endif %}
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Information Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="row g-4">
      <div class="col-lg-4 col-md-6">
        <div class="text-center p-4">
          <i class="fas fa-clock text-primary fa-3x mb-3"></i>
          <h5>Quick Scheduling</h5>
          <p class="text-muted">Book your appointment in just a few minutes with our easy online system</p>
        </div>
      </div>
      
      <div class="col-lg-4 col-md-6">
        <div class="text-center p-4">
          <i class="fas fa-user-md text-success fa-3x mb-3"></i>
          <h5>Expert Doctors</h5>
          <p class="text-muted">Choose from our team of qualified and experienced medical professionals</p>
        </div>
      </div>
      
      <div class="col-lg-4 col-md-6">
        <div class="text-center p-4">
          <i class="fas fa-shield-alt text-info fa-3x mb-3"></i>
          <h5>Secure & Private</h5>
          <p class="text-muted">Your personal and medical information is kept secure and confidential</p>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date to today
    const dateInput = document.getElementById('appointmentDate');
    const today = new Date().toISOString().split('T')[0];
    dateInput.setAttribute('min', today);
    
    // Filter doctors by specialty
    const specialtySelect = document.getElementById('specialty');
    const doctorSelect = document.getElementById('doctor');
    const allDoctorOptions = Array.from(doctorSelect.options);
    
    specialtySelect.addEventListener('change', function() {
        const selectedSpecialty = this.value;
        
        // Clear doctor options
        doctorSelect.innerHTML = '<option value="">Any available doctor</option>';
        
        if (selectedSpecialty) {
            // Add doctors that match the selected specialty
            allDoctorOptions.forEach(option => {
                if (option.dataset.specialty === selectedSpecialty || option.value === '') {
                    doctorSelect.appendChild(option.cloneNode(true));
                }
            });
        } else {
            // Add all doctors back
            allDoctorOptions.forEach(option => {
                doctorSelect.appendChild(option.cloneNode(true));
            });
        }
    });
    
    // Get doctor from URL parameter if present
    const urlParams = new URLSearchParams(window.location.search);
    const doctorId = urlParams.get('doctor');
    if (doctorId) {
        doctorSelect.value = doctorId;
        // Trigger specialty selection based on doctor
        const selectedOption = doctorSelect.options[doctorSelect.selectedIndex];
        if (selectedOption.dataset.specialty) {
            specialtySelect.value = selectedOption.dataset.specialty;
        }
    }
});
</script>
{% endblock content %}
