{% extends 'base.html' %}
{% load static %}

{% block title %}Our Doctors{% endblock title %}

{% block content %}
<!-- <PERSON> Header -->
<section class="bg-primary text-white py-5" style="margin-top: 76px;">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto text-center">
        <h1 class="display-4 fw-bold">Meet Our Doctors</h1>
        <p class="lead">Expert medical professionals dedicated to your health and well-being</p>
      </div>
    </div>
  </div>
</section>

<!-- Doctors Grid -->
<section class="py-5">
  <div class="container">
    {% if doctors %}
      <div class="row g-4">
        {% for doctor in doctors %}
        <div class="col-lg-4 col-md-6">
          <div class="card h-100 border-0 shadow-sm doctor-card">
            <div class="card-body p-4 text-center">
              <div class="doctor-avatar mx-auto mb-3" style="width: 120px; height: 120px; border-radius: 50%; overflow: hidden; border: 4px solid #007bff;">
                {% if doctor.user.profile_avatar %}
                  <img src="{{ doctor.user.profile_avatar.url }}" alt="{{ doctor.user.first_name }}" class="img-fluid w-100 h-100" style="object-fit: cover;">
                {% else %}
                  <div class="bg-primary text-white d-flex align-items-center justify-content-center h-100">
                    <i class="fas fa-user-md fa-3x"></i>
                  </div>
                {% endif %}
              </div>
              
              <h5 class="card-title mb-1">Dr. {{ doctor.user.first_name }} {{ doctor.user.last_name }}</h5>
              <p class="text-primary mb-3">{{ doctor.specialty.name }}</p>
              
              {% if doctor.bio %}
                <p class="card-text text-muted small mb-3">{{ doctor.bio|truncatewords:20 }}</p>
              {% endif %}
              
              <div class="mb-3">
                <small class="text-muted">
                  <i class="fas fa-envelope me-2"></i>{{ doctor.user.email }}
                </small>
              </div>
              
              <div class="d-flex gap-2 justify-content-center">
                <a href="{% url 'appointment' %}?doctor={{ doctor.id }}" class="btn btn-primary btn-sm">
                  <i class="fas fa-calendar-plus me-1"></i>Book Appointment
                </a>
                <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#doctorModal{{ doctor.id }}">
                  <i class="fas fa-info-circle me-1"></i>View Profile
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Doctor Modal -->
        <div class="modal fade" id="doctorModal{{ doctor.id }}" tabindex="-1">
          <div class="modal-dialog modal-lg">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Dr. {{ doctor.user.first_name }} {{ doctor.user.last_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
              </div>
              <div class="modal-body">
                <div class="row">
                  <div class="col-md-4 text-center">
                    <div class="doctor-avatar mx-auto mb-3" style="width: 150px; height: 150px; border-radius: 50%; overflow: hidden; border: 4px solid #007bff;">
                      {% if doctor.user.profile_avatar %}
                        <img src="{{ doctor.user.profile_avatar.url }}" alt="{{ doctor.user.first_name }}" class="img-fluid w-100 h-100" style="object-fit: cover;">
                      {% else %}
                        <div class="bg-primary text-white d-flex align-items-center justify-content-center h-100">
                          <i class="fas fa-user-md fa-4x"></i>
                        </div>
                      {% endif %}
                    </div>
                  </div>
                  <div class="col-md-8">
                    <h4>Dr. {{ doctor.user.first_name }} {{ doctor.user.last_name }}</h4>
                    <p class="text-primary mb-3"><strong>{{ doctor.specialty.name }}</strong></p>
                    
                    {% if doctor.bio %}
                      <h6>About</h6>
                      <p class="text-muted">{{ doctor.bio }}</p>
                    {% endif %}
                    
                    <h6>Contact Information</h6>
                    <ul class="list-unstyled">
                      <li><i class="fas fa-envelope text-primary me-2"></i>{{ doctor.user.email }}</li>
                      {% if doctor.user.id_address %}
                        <li><i class="fas fa-map-marker-alt text-primary me-2"></i>{{ doctor.user.id_address.city }}, {{ doctor.user.id_address.region }}</li>
                      {% endif %}
                    </ul>
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="{% url 'appointment' %}?doctor={{ doctor.id }}" class="btn btn-primary">
                  <i class="fas fa-calendar-plus me-1"></i>Book Appointment
                </a>
              </div>
            </div>
          </div>
        </div>
        {% endfor %}
      </div>
    {% else %}
      <div class="row">
        <div class="col-12 text-center">
          <div class="py-5">
            <i class="fas fa-user-md fa-5x text-muted mb-3"></i>
            <h3 class="text-muted">No Doctors Available</h3>
            <p class="text-muted">Please check back later or contact us for more information.</p>
            <a href="{% url 'contact' %}" class="btn btn-primary">Contact Us</a>
          </div>
        </div>
      </div>
    {% endif %}
  </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="row text-center">
      <div class="col-lg-8 mx-auto">
        <h2 class="display-5 fw-bold mb-3">Ready to Schedule?</h2>
        <p class="lead mb-4">Book an appointment with any of our expert doctors today</p>
        <a href="{% url 'appointment' %}" class="btn btn-primary btn-lg px-4">
          <i class="fas fa-calendar-plus me-2"></i>Book Appointment Now
        </a>
      </div>
    </div>
  </div>
</section>

<style>
.doctor-card {
  transition: all 0.3s ease;
}

.doctor-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
}

.doctor-avatar {
  transition: all 0.3s ease;
}

.doctor-card:hover .doctor-avatar {
  transform: scale(1.05);
}
</style>
{% endblock content %}
