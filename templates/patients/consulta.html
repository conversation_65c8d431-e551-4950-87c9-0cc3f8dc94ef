{% extends 'patients/base.html' %}
{% load static %}
{% block content %}
<main style="display: flex; justify-content: center; align-items: center; min-height: 80vh;">
  <div style="background: #ffffff; padding: 30px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); width: 100%; max-width: 500px; color: black;">
    
    <h2 style="font-size: 24px; text-align: center; margin-bottom: 20px;">
      Consultation Details
    </h2>

    <div style="border: 1px solid #ccc; border-radius: 10px; padding: 20px; margin-bottom: 20px;">
      <p><strong>Doctor:</strong> Dr. {{ appointment.doctor.user.get_full_name }}</p>
      <p><strong>Consultation Time:</strong> {{ appointment.time.duration_minutes }} minutes</p>
      <p><strong>Consultation Fee:</strong> {{ appointment.doctor.consult_fee }}</p>
      <p><strong>Appointment Date:</strong> {{ appointment.start_date| date:"F j, Y"}}</p>
      <p><strong>Appointment Time:</strong> {{appointment.start_date|date:"g:i A"  }}</p>

    </div>

    <form method="post">
      {% csrf_token %}
      <button type="submit" class="btn btn-success w-100" style="font-size: 18px;">Book & Pay</button>
    </form>

  </div>
</main>
{% endblock %}
