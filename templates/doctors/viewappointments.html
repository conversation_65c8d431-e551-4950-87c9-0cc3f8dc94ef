{% extends 'doctors/base.html' %}
{% load static %}

{% block title %}My Appointments{% endblock title %}
{% block page_title %}My Appointments{% endblock page_title %}

{% block content %}
<main>
  <section class="recent">
    <div class="table-div d-flex justify-content-center">
      <div class="col-md-10 col-lg-9 col-xl-8">

        <!-- Filter Form -->
        <div class="filter-section mb-4">
          <form method="get" class="d-md-flex justify-content-between align-items-center text-dark p-3 bg-light rounded shadow">
            <div class="form-group me-3">
              <label for="filter_patient_name" class="me-2 fw-bold">Patient Name:</label>
              <input type="text" name="filter_patient_name" id="filter_patient_name" class="form-control" placeholder="Search Patient" value="{{ filter_patient_name }}">
            </div>
            <div class="form-group me-3">
              <label for="filter_date" class="me-2 fw-bold">Date:</label>
              <input type="date" name="filter_date" id="filter_date" class="form-control" value="{{ filter_date }}">
            </div>
            <button type="submit" class="btn btn-primary mt-3 mt-md-0">Filter</button>
          </form>
        </div>

        <!-- Appointments Table -->
        <div class="bg-white rounded shadow p-3">
          <h4 class="mb-4 text-center fw-bold">Appointments</h4>
          <div class="table-responsive">
            <table class="table table-hover text-center align-middle" id="appointmentsTable">
              <thead class="table-light">
                <tr>
                  <th>Patient</th>
                  <th>Date</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for appointment in appointments %}
                <tr data-start-date="{{ appointment.start_date|date:'c' }}">
                  <td>
                    {{ appointment.patient.user.get_full_name }}
                    {% if forloop.first %}
                      <span class="badge bg-success ms-2">New</span>
                    {% endif %}
                  </td>
                  <td>{{ appointment.start_date }}</td>
                  <td>
                    {% if appointment.payment_status %}
                      <span class="badge bg-success">Paid</span>
                    {% else %}
                      <span class="badge bg-warning text-dark">Unpaid</span>
                    {% endif %}
                  </td>
                  <td>
                    {% if appointment.payment_status %}
                      {% if appointment.consultation %}
                        <a href="{% url 'view_consultation' appointment.id %}" class="btn btn-sm btn-outline-info">View Consultation</a>
                      {% else %}
                        <a href="{% url 'add_consultation' appointment.id %}" class="btn btn-sm btn-outline-primary">Add Consultation</a>
                      {% endif %}
                    {% else %}
                      <span class="text-muted fst-italic">Payment pending</span>
                    {% endif %}
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>

      </div>
    </div>
  </section>
</main>

<!-- Sort Appointments by Start Date (Latest First) -->
<script>
  document.addEventListener("DOMContentLoaded", function () {
    const tbody = document.querySelector("#appointmentsTable tbody");
    const rows = Array.from(tbody.querySelectorAll("tr"));

    rows.sort((a, b) => {
      const dateA = new Date(a.getAttribute("data-start-date"));
      const dateB = new Date(b.getAttribute("data-start-date"));
      return dateB - dateA; // latest first
    });

    rows.forEach(row => tbody.appendChild(row));
  });
</script>
{% endblock %}
