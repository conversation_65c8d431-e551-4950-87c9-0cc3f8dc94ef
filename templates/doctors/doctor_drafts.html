{% extends 'doctors/base.html' %}
{% load static %}

{% block title %} Blogs {% endblock title %}
{% block page_title %} Blogs {% endblock page_title %}

{% block content %}
<main>
  <h2 class="dash-title">Drafted Blogs</h2>
  <section class="recent">
    {% for post in drafts %}
      <div class="table-div">
        <div class="card text-dark mb-3 shadow border-light border" style="border-radius: 5px;">
          <div class="card-header fw-bold" style="background-color: rgb(202, 218, 238);">
            {{ forloop.counter }}.{{ post.title }}</div>
          <div class="card-body row">
            <div class="col-12 col-md-6">
              <img class="img-fluid" src="{{post.thumbnail.url}}" alt="" style="height:40%;width:40%;">
              <table class="table">
                <tbody>
                  <tr>
                    <th scope="col">Title:</th>
                    <td scope="col">{{ post.title }}</td>
                  </tr>
                  <tr>
                    <th scope="col">Description</th>
                    <td scope="col">{{ post.description |safe|truncatechars:100|linebreaks }}</td>
                  </tr>                
                  <tr>
                    <th scope="col">Date</th>
                    <td scope="col">{{ post.posted_at}}</td>
                  </tr>
                  <div class="col-md-12 form-group">
                    <input type="hidden" class="form-control" name='id' value="{{post.id}}"
                      id="website" placeholder="Website (optional)">
                  </div>
                </tbody>
              </table>
            </div>
            <div class="col-12 col-md-6 text-center">
              <h4 class="text-start fs-6"><b>Summary</b></h4>
              <p class="card-text">
                {{ post.summary |safe|truncatechars:200|linebreaks  }}
              </p>
              <a href="{% url 'upload_blog' post.blog_id %}" class="btn btn-outline-dark edit-draft-btn" >
                <b>Edit & Upload</b>
              </a>
            </div>
          </div>
        </div>
      </div>
    {% endfor %}
  </section>
</main>
{% endblock %}
