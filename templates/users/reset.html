{% extends 'users/base.html' %} Forgot Password {% block content %}
<div class="card shadow-lg">
  <div class="card-body p-5">
    <h1 class="fs-4 card-title fw-bold mb-4">Reset Password</h1>
    <form
      method="POST"
      class="needs-validation"
      novalidate=""
      autocomplete="off"
    >
      {% csrf_token %}
      <div class="mb-3">
		<div class="card-body p-5">
			{% if errorlogin %}
		  <div class="alert alert-danger" role="alert">
			Incorrect Details
		  </div>
		  {% endif %}
        <label class="mb-2 text-muted" for="password">New Password</label>
        <input
          id="password"
          type="password"
          class="form-control"
          name="password"
          value=""
          required
          autofocus
        />
        <div class="invalid-feedback">Password is required</div>
      </div>

      <div class="mb-3">
        <label class="mb-2 text-muted" for="password-confirm"
          >Confirm Password</label
        >
        <input
          id="password-confirm"
          type="password"
          class="form-control"
          name="conf_password"
          required
        />
        <div class="invalid-feedback">Please confirm your new password</div>
      </div>

      <div class="d-flex align-items-center">
        <button type="submit" class="btn btn-primary ms-auto">
          Reset Password
        </button>
      </div>
    </form>
  </div>
</div>
{% endblock content %}
