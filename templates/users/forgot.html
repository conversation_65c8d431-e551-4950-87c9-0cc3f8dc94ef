{% extends 'users/base.html' %}

{% block title %}Forgot Password{% endblock title %}

{% block content %}
<div class="card shadow-lg">
  <div class="card-body p-5">
    {% if errorlogin %}
  <div class="alert alert-danger" role="alert">
    Incorrect Details
  </div>
  {% endif %}
  {% if send_email_succes %}
      <div class="alert alert-success" role="alert">
        an email have been sent
      </div>
    {% endif %}
    <h1 class="fs-4 card-title fw-bold mb-4">Forgot Password</h1>
    <form method="POST" class="needs-validation" novalidate="" autocomplete="off">
      {% csrf_token %}
      <div class="mb-3">
        <label class="mb-2 text-muted" for="email">E-Mail Address</label>
        <input id="email" type="email" class="form-control" name="email" value="" required autofocus />
        <div class="invalid-feedback">Email is invalid</div>
      </div>

      <div class="d-flex align-items-center">
        <button type="submit" class="btn btn-primary ms-auto">
          Send Link
        </button>
      </div>
    </form>
  </div>

  <div class="card-footer py-3 border-0">
    <div class="text-center">
      Remember your password?
      <a href={% url 'login' %} class="text-dark">Login</a>
    </div>
  </div>
</div>
{% endblock content %}

