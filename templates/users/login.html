{% extends 'users/base.html' %} {% block title %}Login Page{% endblock title %}
{% block content %}
<div class="card shadow-lg mx-auto mt-5" style="max-width: 24rem">
  <div class="card-body p-4">
    <h1 class="fs-4 card-title fw-bold mb-3 text-center">Login</h1>

    <form
      method="POST"
      class="needs-validation"
      novalidate=""
      autocomplete="off"
      enctype="multipart/form-data"
    >
      {% csrf_token %} {% if messages %} {% for message in messages %}
      <div
        class="alert alert-danger alert-dismissible fade show mt-3"
        role="alert"
      >
        {{ message }}
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
          aria-label="Close"
        ></button>
      </div>
      {% endfor %} {% endif %}

      <div class="mb-3">
        <label class="mb-2 text-muted" for="email">User ID</label>
        <input
          id="email"
          type="text"
          class="form-control"
          name="username"
          value=""
          required
          autofocus
        />
        <div class="invalid-feedback">User ID is is invalid</div>
      </div>

      <div class="mb-3">
        <div class="mb-2 w-100">
          <label class="text-muted" for="password">Password</label>
          <a href="{% url 'password-reset' %}" class="float-end primary-color"
            >Forgot Password?</a
          >
        </div>

        <input
          id="password"
          type="password"
          class="form-control"
          name="password"
          required
        />
        <div class="invalid-feedback">Password is required</div>
      </div>

      <div class="d-flex align-items-center">
        <div class="form-check">
          <label for="remember" class="form-check-label"></label>
        </div>

        <button
          type="submit"
          class="btn btn-primary ms-auto primary-color-background"
        >
          Login
        </button>
      </div>
    </form>
  </div>
  <div class="card-footer py-3 border-0">
    <div class="text-center">
      Don't have an account?
      <a href="{% url 'register' %}" class="text-dark secondary-color"
        >Sign Up</a
      >
    </div>
    <div class="text-center mt-2">
      <a href="{% url 'home' %}" class="text-muted text-decoration-none">
        <i class="fas fa-arrow-left me-1"></i>Back to Home
      </a>
    </div>
  </div>
</div>
{% endblock content %}
