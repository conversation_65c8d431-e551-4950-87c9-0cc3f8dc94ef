{% extends 'users/base.html' %}

{% block title %}Register Page{% endblock title %}

{% block content %}
<div class="card shadow-lg mx-auto mt-5" style="max-width: 34rem;">
  <div class="card-body p-4">
    <h1 class="fs-4 card-title fw-bold mb-3 text-center">Register</h1>

    <form method="POST" class="needs-validation" novalidate="" autocomplete="off" enctype="multipart/form-data">
      {% csrf_token %}

      {% if messages %}
        {% for message in messages %}
          {% if 'success' in message.tags %}
            <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
              {{ message }}
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
          {% else %}
            <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
              {{ message }}
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
          {% endif %}
        {% endfor %}
      {% endif %}

      <div class="mb-3">
        <label class="mb-2 text-muted" for="user_status">Choose Your Status :</label>
        <select class="form-select" name="user_config" id="user_status">
          <option value="Doctor" {% if user_config == 'Doctor' %}selected{% endif %}>Doctor</option>
          <option value="Patient" {% if user_config == 'Patient' %}selected{% endif %}>Patient</option>
        </select>
      </div>

      <div id="specialty_block" class="mb-3" style="display: {% if user_config == 'Doctor' %}block{% else %}none{% endif %}">
        <label class="mb-2 text-muted" for="specialty">Specialty</label>
        <select name="Speciality" class="form-select" id="">
          {% for speciality in specialities %}
          <option value="{{speciality.name}}">{{speciality.name}}</option>
          {%endfor%}
        </select>
        <div class="invalid-feedback">Specialty is required for doctors</div>
      </div>
      
      <div id="bio_block" class="mb-3" style="display: {% if user_config == 'Doctor' %}block{% else %}none{% endif %}">
          <label class="mb-2 text-muted" for="bio">Bio</label>
          <textarea id="bio" class="form-control" name="bio" {% if user_config == 'Doctor' %}required{% endif %}></textarea>
          <div class="invalid-feedback">Bio is required for doctors</div>
      </div>
      
      <div id="insurance_block" class="mb-3" style="display: {% if user_config == 'Patient' %}block{% else %}none{% endif %}">
          <label class="mb-2 text-muted" for="insurance">Insurance</label>
          <input id="insurance" type="text" class="form-control" name="insurance" {% if user_config == 'Patient' %}required{% endif %} />
          <div class="invalid-feedback">Insurance is required for patients</div>
      </div>

      <div class="mb-3">
        <label class="mb-2 text-muted" for="id_user">Username</label>
        <input id="id_user" type="text" class="form-control" name="user_id" value="{{ user_id }}" required autofocus />
        <div class="invalid-feedback">username is required</div>
      </div>

      <div class="mb-3">
        <label class="mb-2 text-muted" for="first_name">First Name</label>
        <input id="first_name" type="text" class="form-control" name="user_firstname" value="{{ user_firstname }}" required autofocus />
        <div class="invalid-feedback">First Name is required</div>
      </div>

      <div class="mb-3">
        <label class="mb-2 text-muted" for="last_name">Last Name</label>
        <input id="last_name" type="text" class="form-control" name="user_lastname" value="{{ user_lastname }}" required autofocus />
        <div class="invalid-feedback">Last Name is required</div>
      </div>
      
      <div class="mb-3">
        <label class="mb-2 text-muted" for="select_gender">Gender</label>
        <select class="form-select" aria-label="Default select example" name="user_gender" id="select_gender">
          <option value="Male" {% if user_gender == 'Male' %}selected{% endif %}>Male</option>
          <option value="Female" {% if user_gender == 'Female' %}selected{% endif %}>Female</option>
        </select>
      </div>   
      
      <div class="mb-3" >
        <label class="mb-2 text-muted" for="birthday">Birthday</label>
        <input id="birthday" type="date" class="form-control" name="birthday" value="{{ birthday }}" required />
        <div class="invalid-feedback">Birthday is invalid</div>
      </div>
      
      <div class="mb-3" >
        <label class="mb-2 text-muted" for="email">E-mail Address</label>
        <input id="email" type="email" class="form-control" name="email" value="{{ email }}" required />
        <div class="invalid-feedback">Email is invalid</div>
      </div>

      <div class="mb-3">
        <label class="mb-2 text-muted" for="password">Password</label>
        <input id="password" type="password" class="form-control" name="password" required />
        <div class="invalid-feedback">Password is required</div>
      </div>

      <div class="mb-3">
        <label class="mb-2 text-muted" for="conf__password">Confirm Password</label>
        <input id="conf__password" type="password" class="form-control" name="conf_password" required />
        <div class="invalid-feedback">Password is required</div>
      </div>

      <div class="mb-3">
        <label class="mb-2 text-muted" for="AddressLine">Address Line</label>
        <input id="AddressLine" type="text" class="form-control mb-2" name="address_line" value="{{ address_line }}" placeholder="Address Line" required autofocus />
        <input id="Region" type="text" class="form-control mb-2" name="region" value="{{ region }}" placeholder="Region" required autofocus />
        <input id="City" type="text" class="form-control mb-2" name="city" value="{{ city }}" placeholder="City" required autofocus />
        <input id="Pincode" type="text" class="form-control" name="pincode" value="{{ pincode }}" placeholder="Code Postal" required autofocus />
        <div class="invalid-feedback">Address is required</div>
      </div>
      
      <div class="mb-3 col-12 col-md-12">
        <label for="pic" class="form-label text-muted">Profile Avatar</label>
        <input type="file" class="form-control p-3"  name="profile_pic" id="pic"  required>
      </div>

      <p class="form-text text-muted mb-3">
        By registering you agree with our terms and condition.
      </p>

      <div class="align-items-center d-flex">
        <button type="submit" class="btn btn-primary ms-auto primary-color-background">
          Register
        </button>
      </div>
    </form>
  </div>
  <div class="card-footer py-3 border-0">
    <div class="text-center">
      Already have an account?
      <a href="{% url 'login' %}" class="text-dark secondary-color">Login</a>
    </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function() {
    toggleFields();

    document.getElementById('user_status').addEventListener('change', function() {
      toggleFields();
    });

    function toggleFields() {
      var userConfig = document.getElementById('user_status').value;
      var specialtyBlock = document.getElementById('specialty_block');
      var bioBlock = document.getElementById('bio_block');
      var insuranceBlock = document.getElementById('insurance_block');

      if (userConfig === 'Doctor') {
        specialtyBlock.style.display = 'block';
        bioBlock.style.display = 'block';
        insuranceBlock.style.display = 'none';
      } else if (userConfig === 'Patient') {
        specialtyBlock.style.display = 'none';
        bioBlock.style.display = 'none';
        insuranceBlock.style.display = 'block';
      }
    }
  });
</script>


{% endblock content %}
