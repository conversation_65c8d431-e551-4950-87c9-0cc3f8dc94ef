/* Front Page Styles */

/* General Styles */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
}

/* Navigation Styles */
.navbar {
  transition: all 0.3s ease;
  padding: 1rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.navbar-brand img {
  transition: all 0.3s ease;
  filter: brightness(1.1);
}

.navbar-nav .nav-link {
  font-weight: 600;
  margin: 0 0.5rem;
  transition: all 0.3s ease;
  color: white !important;
  position: relative;
  padding: 0.75rem 1rem !important;
  border-radius: 25px;
}

.navbar-nav .nav-link:hover {
  color: #ffd700 !important;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.navbar-nav .nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 5px;
  left: 50%;
  background: #ffd700;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
  width: 80%;
}

.btn-outline-primary {
  border-radius: 25px;
  padding: 0.5rem 1.5rem;
  font-weight: 600;
  border: 2px solid #ffd700;
  color: #ffd700 !important;
  background: transparent;
  transition: all 0.3s ease;
}

.btn-outline-primary:hover {
  background: #ffd700;
  color: #333 !important;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.btn-primary {
  border-radius: 25px;
  padding: 0.5rem 1.5rem;
  font-weight: 600;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  border: none;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #ee5a24, #ff6b6b);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  min-height: 90vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
  background-size: cover;
}

.min-vh-75 {
  min-height: 85vh;
  position: relative;
  z-index: 2;
}

.hero-section h1 {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #fff, #ffd700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-section .lead {
  font-size: 1.4rem;
  opacity: 0.95;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  margin-bottom: 2rem;
}

.hero-section .btn {
  border-radius: 50px;
  padding: 1.2rem 2.5rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.hero-section .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.hero-section .btn:hover::before {
  left: 100%;
}

.hero-section .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.hero-section .btn-light {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333 !important;
  border: none;
}

.hero-section .btn-outline-light {
  border: 3px solid #ffd700;
  color: #ffd700 !important;
  background: transparent;
}

.hero-section .btn-outline-light:hover {
  background: #ffd700;
  color: #333 !important;
}

/* Feature Cards */
.card {
  transition: all 0.4s ease;
  border-radius: 20px;
  border: none;
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
}

.card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  transition: all 0.4s ease;
  position: relative;
}

.card:hover .feature-icon {
  transform: scale(1.15) rotate(5deg);
}

/* Enhanced Feature Icons with Gradients */
.feature-icon.bg-primary {
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
}

.feature-icon.bg-success {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf) !important;
}

.feature-icon.bg-info {
  background: linear-gradient(135deg, #00c6ff, #0072ff) !important;
}

/* Service Cards */
.service-card {
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  border-radius: 20px;
  transition: all 0.4s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  padding: 2rem 1.5rem;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-card:hover {
  transform: translateY(-10px) scale(1.03);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  color: white;
}

.service-card:hover::before {
  opacity: 1;
}

.service-card i {
  transition: all 0.4s ease;
  margin-bottom: 1rem;
}

.service-card:hover i {
  transform: scale(1.3) rotate(10deg);
  color: white !important;
}

.service-card h5 {
  transition: color 0.3s ease;
  font-weight: 700;
}

.service-card:hover h5 {
  color: white;
}

.service-card p {
  transition: color 0.3s ease;
}

.service-card:hover p {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Sections */
section {
  padding: 6rem 0;
  position: relative;
}

.bg-light {
  background: linear-gradient(135deg, #f093fb 0%, #f5f7fa 50%, #c3cfe2 100%) !important;
}

/* Colorful Section Backgrounds */
.features-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.features-section .card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.services-section {
  background: linear-gradient(135deg, #f093fb 0%, #f5f7fa 50%, #c3cfe2 100%);
  position: relative;
}

.services-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20" fill="rgba(255,255,255,0.1)"><circle cx="10" cy="10" r="2"/><circle cx="30" cy="10" r="2"/><circle cx="50" cy="10" r="2"/><circle cx="70" cy="10" r="2"/><circle cx="90" cy="10" r="2"/></svg>');
  opacity: 0.3;
}

.cta-section {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff9ff3 100%);
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes slideInLeft {
  0% { transform: translateX(-100px); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
  0% { transform: translateX(100px); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes fadeInUp {
  0% { transform: translateY(50px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

/* Animation Classes */
.animated-logo {
  animation: float 3s ease-in-out infinite;
}

.floating-element {
  animation: float 2s ease-in-out infinite;
}

.slide-in-left {
  animation: slideInLeft 1s ease-out;
}

.slide-in-right {
  animation: slideInRight 1s ease-out;
}

.fade-in-up {
  animation: fadeInUp 1s ease-out;
}

/* Feature Badges */
.feature-badge {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.feature-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Service Icon Wrappers */
.service-icon-wrapper {
  transition: all 0.4s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.service-card:hover .service-icon-wrapper {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Specific Service Card Hover Effects */
.cardiology-card:hover {
  box-shadow: 0 15px 35px rgba(231, 76, 60, 0.3);
}

.neurology-card:hover {
  box-shadow: 0 15px 35px rgba(52, 152, 219, 0.3);
}

.pediatrics-card:hover {
  box-shadow: 0 15px 35px rgba(46, 204, 113, 0.3);
}

.surgery-card:hover {
  box-shadow: 0 15px 35px rgba(243, 156, 18, 0.3);
}

/* Navbar Scrolled State */
.navbar.scrolled {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.95), rgba(118, 75, 162, 0.95)) !important;
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Enhanced Button Styles */
.btn-lg {
  padding: 1.2rem 2.5rem;
  font-size: 1.1rem;
  border-radius: 50px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.btn-lg::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-lg:hover::before {
  left: 100%;
}

/* Buttons */
.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.btn-lg:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Footer */
footer {
  background: linear-gradient(135deg, #2c3e50, #34495e) !important;
}

footer h5, footer h6 {
  color: #fff;
  font-weight: 600;
}

footer .social-links a {
  display: inline-block;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  transition: all 0.3s ease;
}

footer .social-links a:hover {
  background: #007bff;
  transform: translateY(-3px);
}

footer ul li {
  margin-bottom: 0.5rem;
}

footer ul li a:hover {
  color: #007bff !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section h1 {
    font-size: 2.5rem;
  }
  
  .hero-section .lead {
    font-size: 1.1rem;
  }
  
  .navbar-nav .nav-link {
    margin: 0.25rem 0;
  }
  
  section {
    padding: 3rem 0;
  }
}

@media (max-width: 576px) {
  .hero-section h1 {
    font-size: 2rem;
  }
  
  .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
  
  .display-5 {
    font-size: 2rem;
  }
}

/* Animation Classes */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #007bff;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0056b3;
}

/* Image Placeholders */
.hero-image-placeholder,
.about-image-placeholder {
  transition: all 0.3s ease;
  border: 2px dashed #dee2e6;
}

.hero-image-placeholder:hover,
.about-image-placeholder:hover {
  border-color: #007bff;
  background-color: #f8f9fa !important;
}

/* Contact Icons */
.contact-icon {
  transition: all 0.3s ease;
}

.contact-icon:hover {
  transform: scale(1.1);
}

/* Doctor Cards */
.doctor-card {
  transition: all 0.3s ease;
}

.doctor-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
}

.doctor-avatar {
  transition: all 0.3s ease;
}

.doctor-card:hover .doctor-avatar {
  transform: scale(1.05);
}

/* Form Enhancements */
.form-control:focus,
.form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  border: none;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

/* Alert Enhancements */
.alert {
  border: none;
  border-radius: 10px;
}

.alert-info {
  background: linear-gradient(135deg, #d1ecf1, #bee5eb);
  color: #0c5460;
}

/* Loading States */
.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Utility Classes */
.text-shadow {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.box-shadow-lg {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* Logo Fallback Styles */
.logo-fallback {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-weight: bold;
  font-size: 20px;
  color: #007bff;
  text-decoration: none;
  display: inline-block;
  padding: 5px 10px;
  border-radius: 5px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px solid #007bff;
  transition: all 0.3s ease;
}

.logo-fallback:hover {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  transform: scale(1.05);
}

.logo-fallback .go { color: #28a745; }
.logo-fallback .doctor { color: #007bff; }
.logo-fallback .care { color: #dc3545; }

.logo-fallback:hover .go,
.logo-fallback:hover .doctor,
.logo-fallback:hover .care {
  color: white;
}

/* White logo version for dark backgrounds */
.logo-fallback-white {
  color: white;
  border-color: white;
  background: rgba(255, 255, 255, 0.1);
}

.logo-fallback-white .go { color: #90ee90; }
.logo-fallback-white .doctor { color: #87ceeb; }
.logo-fallback-white .care { color: #ffb6c1; }
