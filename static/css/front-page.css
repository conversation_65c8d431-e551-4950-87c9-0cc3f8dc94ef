/* Front Page Styles */

/* General Styles */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
}

/* Navigation Styles */
.navbar {
  transition: all 0.3s ease;
  padding: 1rem 0;
}

.navbar-brand img {
  transition: all 0.3s ease;
}

.navbar-nav .nav-link {
  font-weight: 500;
  margin: 0 0.5rem;
  transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
  color: #007bff !important;
}

.btn-outline-primary {
  border-radius: 25px;
  padding: 0.5rem 1.5rem;
  font-weight: 500;
}

.btn-primary {
  border-radius: 25px;
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  background: linear-gradient(135deg, #007bff, #0056b3);
  border: none;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #007bff, #0056b3);
  min-height: 80vh;
  display: flex;
  align-items: center;
}

.min-vh-75 {
  min-height: 75vh;
}

.hero-section h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.hero-section .lead {
  font-size: 1.25rem;
  opacity: 0.9;
}

.hero-section .btn {
  border-radius: 50px;
  padding: 1rem 2rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.hero-section .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Feature Cards */
.card {
  transition: all 0.3s ease;
  border-radius: 15px;
}

.card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  transition: all 0.3s ease;
}

.card:hover .feature-icon {
  transform: scale(1.1);
}

/* Service Cards */
.service-card {
  background: white;
  border-radius: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.service-card i {
  transition: all 0.3s ease;
}

.service-card:hover i {
  transform: scale(1.2);
}

/* Sections */
section {
  padding: 5rem 0;
}

.bg-light {
  background-color: #f8f9fa !important;
}

/* Buttons */
.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.btn-lg:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Footer */
footer {
  background: linear-gradient(135deg, #2c3e50, #34495e) !important;
}

footer h5, footer h6 {
  color: #fff;
  font-weight: 600;
}

footer .social-links a {
  display: inline-block;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  transition: all 0.3s ease;
}

footer .social-links a:hover {
  background: #007bff;
  transform: translateY(-3px);
}

footer ul li {
  margin-bottom: 0.5rem;
}

footer ul li a:hover {
  color: #007bff !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section h1 {
    font-size: 2.5rem;
  }
  
  .hero-section .lead {
    font-size: 1.1rem;
  }
  
  .navbar-nav .nav-link {
    margin: 0.25rem 0;
  }
  
  section {
    padding: 3rem 0;
  }
}

@media (max-width: 576px) {
  .hero-section h1 {
    font-size: 2rem;
  }
  
  .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
  
  .display-5 {
    font-size: 2rem;
  }
}

/* Animation Classes */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #007bff;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0056b3;
}

/* Image Placeholders */
.hero-image-placeholder,
.about-image-placeholder {
  transition: all 0.3s ease;
  border: 2px dashed #dee2e6;
}

.hero-image-placeholder:hover,
.about-image-placeholder:hover {
  border-color: #007bff;
  background-color: #f8f9fa !important;
}

/* Contact Icons */
.contact-icon {
  transition: all 0.3s ease;
}

.contact-icon:hover {
  transform: scale(1.1);
}

/* Doctor Cards */
.doctor-card {
  transition: all 0.3s ease;
}

.doctor-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
}

.doctor-avatar {
  transition: all 0.3s ease;
}

.doctor-card:hover .doctor-avatar {
  transform: scale(1.05);
}

/* Form Enhancements */
.form-control:focus,
.form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  border: none;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

/* Alert Enhancements */
.alert {
  border: none;
  border-radius: 10px;
}

.alert-info {
  background: linear-gradient(135deg, #d1ecf1, #bee5eb);
  color: #0c5460;
}

/* Loading States */
.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Utility Classes */
.text-shadow {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.box-shadow-lg {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* Logo Fallback Styles */
.logo-fallback {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-weight: bold;
  font-size: 20px;
  color: #007bff;
  text-decoration: none;
  display: inline-block;
  padding: 5px 10px;
  border-radius: 5px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px solid #007bff;
  transition: all 0.3s ease;
}

.logo-fallback:hover {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  transform: scale(1.05);
}

.logo-fallback .go { color: #28a745; }
.logo-fallback .doctor { color: #007bff; }
.logo-fallback .care { color: #dc3545; }

.logo-fallback:hover .go,
.logo-fallback:hover .doctor,
.logo-fallback:hover .care {
  color: white;
}

/* White logo version for dark backgrounds */
.logo-fallback-white {
  color: white;
  border-color: white;
  background: rgba(255, 255, 255, 0.1);
}

.logo-fallback-white .go { color: #90ee90; }
.logo-fallback-white .doctor { color: #87ceeb; }
.logo-fallback-white .care { color: #ffb6c1; }
